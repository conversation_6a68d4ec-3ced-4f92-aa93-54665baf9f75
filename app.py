#!/usr/bin/env python3
"""
Mürşid Giriş Uygulaması - Mürşid Login Application
"""

import flet as ft

def main(page: ft.Page):
    page.title = "<PERSON>iri<PERSON> Yap - Mürşid"
    page.vertical_alignment = ft.MainAxisAlignment.CENTER
    page.horizontal_alignment = ft.CrossAxisAlignment.CENTER
    page.window_width = 350  # Varsayılan boyut
    page.window_height = 500  # Varsayılan boyut
    page.window_resizable = True  # Boyut değiştirilebilir
    page.window_min_width = 280  # Minimum genişlik
    page.window_min_height = 400  # Minimum yükseklik
    page.padding = 10  # Sayfa dolgusu

    # Kullanıcı verilerini kaydetmek için değişkenler
    if not hasattr(page, 'current_language'):
        page.current_language = "tr"  # Varsayılan dil: Türkçe
    current_language = page.current_language

    print(f"Current language: {current_language}")  # للتحقق من اللغة الحالية

    # Dil metinleri
    texts = {
        "tr": {
            "title": "<PERSON><PERSON><PERSON> - Mü<PERSON>şid",
            "login_title": "<PERSON><PERSON><PERSON> Yap",
            "welcome": "<PERSON><PERSON>rş<PERSON> uygulamasına hoş geldiniz",
            "email_label": "E-posta",
            "email_hint": "<EMAIL>",
            "password_label": "Şifre",
            "password_hint": "Şifrenizi girin",
            "login_button": "Giriş Yap",
            "register_button": "Yeni hesap oluştur",
            "forgot_button": "Şifremi unuttum",
            "email_error": "Lütfen e-posta adresinizi girin",
            "password_error": "Lütfen şifrenizi girin",
            "success_message": "Hoş geldiniz! Giriş başarılı\nE-posta: ",
            "admin_welcome": "Yönetici Paneline Hoş Geldiniz!",
            "admin_dashboard": "Yönetici Paneli",
            "admin_users": "Kullanıcı Yönetimi",
            "admin_settings": "Sistem Ayarları",
            "admin_reports": "Raporlar",
            "admin_logs": "Sistem Günlükleri",
            "admin_backup": "Yedekleme",
            "admin_security": "Güvenlik",
            "admin_logout": "Çıkış Yap",
            "admin_user_count": "Toplam Kullanıcı",
            "admin_active_sessions": "Aktif Oturumlar",
            "admin_system_status": "Sistem Durumu",
            "admin_last_backup": "Son Yedekleme",
            "about": "Hakkında",
            "help": "Yardım",
            "settings": "Ayarlar",
            "language": "Dil",
            "theme": "Tema",
            "privacy": "Gizlilik",
            "about_text": "Mürşid v1.0 - Güvenli Giriş Uygulaması",
            "help_text": "Yardım: E-posta ve şifrenizi girin",
            "settings_text": "Ayarlar sayfası yakında eklenecek",
            "theme_text": "Tema değiştirme yakında eklenecek",
            "privacy_text": "Gizlilik Politikası yakında eklenecek",
            "register_text": "Kayıt sayfası yakında eklenecek",
            "forgot_text": "Şifre sıfırlama yakında eklenecek",
            "language_name": "Türkçe"
        },
        "ar": {
            "title": "تسجيل الدخول - Mürşid",
            "login_title": "تسجيل الدخول",
            "welcome": "مرحباً بك في تطبيق Mürşid",
            "email_label": "البريد الإلكتروني",
            "email_hint": "<EMAIL>",
            "password_label": "كلمة المرور",
            "password_hint": "أدخل كلمة المرور",
            "login_button": "تسجيل الدخول",
            "register_button": "إنشاء حساب جديد",
            "forgot_button": "نسيت كلمة المرور؟",
            "email_error": "يرجى إدخال البريد الإلكتروني",
            "password_error": "يرجى إدخال كلمة المرور",
            "success_message": "مرحباً! تم تسجيل الدخول بنجاح\nالبريد الإلكتروني: ",
            "admin_welcome": "مرحباً بك في لوحة الإدارة!",
            "admin_dashboard": "لوحة الإدارة",
            "admin_users": "إدارة المستخدمين",
            "admin_settings": "إعدادات النظام",
            "admin_reports": "التقارير",
            "admin_logs": "سجلات النظام",
            "admin_backup": "النسخ الاحتياطي",
            "admin_security": "الأمان",
            "admin_logout": "تسجيل الخروج",
            "admin_user_count": "إجمالي المستخدمين",
            "admin_active_sessions": "الجلسات النشطة",
            "admin_system_status": "حالة النظام",
            "admin_last_backup": "آخر نسخة احتياطية",
            "about": "حول التطبيق",
            "help": "المساعدة",
            "settings": "الإعدادات",
            "language": "اللغة",
            "theme": "المظهر",
            "privacy": "الخصوصية",
            "about_text": "Mürşid الإصدار 1.0 - تطبيق دخول آمن",
            "help_text": "المساعدة: أدخل بريدك الإلكتروني وكلمة المرور",
            "settings_text": "صفحة الإعدادات ستضاف قريباً",
            "theme_text": "تغيير المظهر سيضاف قريباً",
            "privacy_text": "سياسة الخصوصية ستضاف قريباً",
            "register_text": "صفحة التسجيل ستضاف قريباً",
            "forgot_text": "استعادة كلمة المرور ستضاف قريباً",
            "language_name": "العربية"
        },
        "en": {
            "title": "Login - Mürşid",
            "login_title": "Login",
            "welcome": "Welcome to Mürşid application",
            "email_label": "Email",
            "email_hint": "<EMAIL>",
            "password_label": "Password",
            "password_hint": "Enter your password",
            "login_button": "Login",
            "register_button": "Create new account",
            "forgot_button": "Forgot password?",
            "email_error": "Please enter your email",
            "password_error": "Please enter your password",
            "success_message": "Welcome! Login successful\nEmail: ",
            "admin_welcome": "Welcome to Admin Dashboard!",
            "admin_dashboard": "Admin Dashboard",
            "admin_users": "User Management",
            "admin_settings": "System Settings",
            "admin_reports": "Reports",
            "admin_logs": "System Logs",
            "admin_backup": "Backup",
            "admin_security": "Security",
            "admin_logout": "Logout",
            "admin_user_count": "Total Users",
            "admin_active_sessions": "Active Sessions",
            "admin_system_status": "System Status",
            "admin_last_backup": "Last Backup",
            "about": "About",
            "help": "Help",
            "settings": "Settings",
            "language": "Language",
            "theme": "Theme",
            "privacy": "Privacy",
            "about_text": "Mürşid v1.0 - Secure Login Application",
            "help_text": "Help: Enter your email and password",
            "settings_text": "Settings page coming soon",
            "theme_text": "Theme changing coming soon",
            "privacy_text": "Privacy Policy coming soon",
            "register_text": "Registration page coming soon",
            "forgot_text": "Password reset coming soon",
            "language_name": "English"
        }
    }

    # Sayfa başlığını ayarla
    page.title = texts[current_language]["title"]



    # Ayarlar menüsü fonksiyonları
    def show_about(e):
        print("About clicked!")  # للتحقق

        # استخدام صفحة جديدة لحول التطبيق
        def close_about():
            page.clean()
            main(page)



        # مسح الصفحة الحالية وإظهار صفحة حول التطبيق
        page.clean()

        page.add(
            ft.Container(
                content=ft.Column([
                    # شريط علوي مع زر الرجوع
                    ft.Row([
                        ft.IconButton(
                            icon=ft.Icons.ARROW_BACK,
                            on_click=lambda e: close_about()
                        ),
                        ft.Text(
                            "Hakkında" if current_language == "tr"
                            else "حول التطبيق" if current_language == "ar"
                            else "About",
                            size=20, weight=ft.FontWeight.BOLD
                        ),
                        ft.Container(expand=True),
                    ]),

                    ft.Divider(),

                    # شعار التطبيق
                    ft.Text("🛡️", size=80, text_align=ft.TextAlign.CENTER),

                    ft.Text(
                        "Mürşid",
                        size=32,
                        weight=ft.FontWeight.BOLD,
                        text_align=ft.TextAlign.CENTER,
                        color=ft.Colors.BLUE_700
                    ),

                    ft.Text(
                        "Güvenli Giriş Uygulaması" if current_language == "tr"
                        else "تطبيق دخول آمن" if current_language == "ar"
                        else "Secure Login Application",
                        size=16,
                        text_align=ft.TextAlign.CENTER,
                        color=ft.Colors.GREY_600
                    ),

                    ft.Container(height=20),

                    # معلومات الإصدار
                    ft.Container(
                        content=ft.Column([
                            ft.Text(
                                "📱 Sürüm Bilgileri" if current_language == "tr"
                                else "📱 معلومات الإصدار" if current_language == "ar"
                                else "📱 Version Info",
                                size=16,
                                weight=ft.FontWeight.BOLD,
                                color=ft.Colors.GREEN_700
                            ),
                            ft.Text(
                                "Sürüm: 1.0.0\nYayın Tarihi: Ocak 2024\nPlatform: Windows, macOS, Linux" if current_language == "tr"
                                else "الإصدار: 1.0.0\nتاريخ الإصدار: يناير 2024\nالمنصة: Windows, macOS, Linux" if current_language == "ar"
                                else "Version: 1.0.0\nRelease Date: January 2024\nPlatform: Windows, macOS, Linux",
                                size=14,
                                color=ft.Colors.GREY_700,
                                text_align=ft.TextAlign.CENTER
                            )
                        ], spacing=10),
                        padding=15,
                        bgcolor=ft.Colors.GREEN_50,
                        border_radius=10,
                        border=ft.border.all(1, ft.Colors.GREEN_200)
                    ),

                    ft.Container(height=15),

                    # وصف التطبيق
                    ft.Container(
                        content=ft.Column([
                            ft.Text(
                                "📝 Açıklama" if current_language == "tr"
                                else "📝 الوصف" if current_language == "ar"
                                else "📝 Description",
                                size=16,
                                weight=ft.FontWeight.BOLD,
                                color=ft.Colors.BLUE_700
                            ),
                            ft.Text(
                                "Mürşid, kullanıcıların güvenli bir şekilde giriş yapabilmelerini sağlayan modern bir uygulamadır. Gelişmiş güvenlik özellikleri, çok dilli destek ve kullanıcı dostu arayüzü ile öne çıkar." if current_language == "tr"
                                else "مرشد هو تطبيق حديث يمكّن المستخدمين من تسجيل الدخول بأمان. يتميز بميزات أمان متقدمة ودعم متعدد اللغات وواجهة سهلة الاستخدام." if current_language == "ar"
                                else "Mürşid is a modern application that enables users to log in securely. It stands out with advanced security features, multi-language support, and user-friendly interface.",
                                size=14,
                                color=ft.Colors.GREY_700,
                                text_align=ft.TextAlign.CENTER
                            )
                        ], spacing=10),
                        padding=15,
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=10,
                        border=ft.border.all(1, ft.Colors.BLUE_200)
                    ),

                    ft.Container(height=15),

                    # الميزات
                    ft.Text(
                        "✨ Özellikler" if current_language == "tr"
                        else "✨ الميزات" if current_language == "ar"
                        else "✨ Features",
                        size=16,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.PURPLE_600
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.SECURITY, color=ft.Colors.GREEN),
                        title=ft.Text(
                            "Gelişmiş Güvenlik" if current_language == "tr"
                            else "أمان متقدم" if current_language == "ar"
                            else "Advanced Security"
                        ),
                        subtitle=ft.Text(
                            "İki faktörlü kimlik doğrulama ve şifreleme" if current_language == "tr"
                            else "مصادقة ثنائية وتشفير" if current_language == "ar"
                            else "Two-factor authentication and encryption"
                        )
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.LANGUAGE, color=ft.Colors.BLUE),
                        title=ft.Text(
                            "Çok Dilli Destek" if current_language == "tr"
                            else "دعم متعدد اللغات" if current_language == "ar"
                            else "Multi-language Support"
                        ),
                        subtitle=ft.Text(
                            "Türkçe, Arapça ve İngilizce" if current_language == "tr"
                            else "التركية والعربية والإنجليزية" if current_language == "ar"
                            else "Turkish, Arabic, and English"
                        )
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.PRIVACY_TIP, color=ft.Colors.ORANGE),
                        title=ft.Text(
                            "Gizlilik Koruması" if current_language == "tr"
                            else "حماية الخصوصية" if current_language == "ar"
                            else "Privacy Protection"
                        ),
                        subtitle=ft.Text(
                            "Verileriniz güvende ve korunuyor" if current_language == "tr"
                            else "بياناتك آمنة ومحمية" if current_language == "ar"
                            else "Your data is safe and protected"
                        )
                    ),

                    ft.Container(height=15),

                    # معلومات التواصل
                    ft.Text(
                        "📞 İletişim" if current_language == "tr"
                        else "📞 التواصل" if current_language == "ar"
                        else "📞 Contact",
                        size=16,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.TEAL_600
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.EMAIL, color=ft.Colors.TEAL),
                        title=ft.Text("<EMAIL>"),
                        subtitle=ft.Text(
                            "Destek ve yardım için" if current_language == "tr"
                            else "للدعم والمساعدة" if current_language == "ar"
                            else "For support and help"
                        )
                    ),

                    ft.Container(height=20),

                    # حقوق النشر
                    ft.Container(
                        content=ft.Text(
                            "© 2024 Mürşid Team. Tüm hakları saklıdır." if current_language == "tr"
                            else "© 2024 فريق مرشد. جميع الحقوق محفوظة." if current_language == "ar"
                            else "© 2024 Mürşid Team. All rights reserved.",
                            size=12,
                            color=ft.Colors.GREY_500,
                            text_align=ft.TextAlign.CENTER
                        ),
                        padding=10
                    ),

                    # زر الرجوع
                    ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.ARROW_BACK),
                            ft.Text(
                                "Geri Dön" if current_language == "tr"
                                else "العودة" if current_language == "ar"
                                else "Go Back"
                            )
                        ], alignment=ft.MainAxisAlignment.CENTER),
                        width=200,
                        on_click=lambda e: close_about()
                    ),

                    # مساحة إضافية في الأسفل
                    ft.Container(height=30)

                ], spacing=10, scroll=ft.ScrollMode.ALWAYS),
                padding=20,
                expand=True
            )
        )

    # Dil seçimi fonksiyonları
    def change_to_turkish(e):
        if hasattr(page, 'dialog') and page.dialog:
            page.dialog.open = False
        page.current_language = "tr"
        page.snack_bar = ft.SnackBar(content=ft.Text("Dil Türkçe olarak değiştirildi"))
        page.snack_bar.open = True
        page.update()
        # Sayfayı yeniden yükle
        import time
        time.sleep(0.5)
        page.clean()
        main(page)

    def change_to_arabic(e):
        if hasattr(page, 'dialog') and page.dialog:
            page.dialog.open = False
        page.current_language = "ar"
        page.snack_bar = ft.SnackBar(content=ft.Text("تم تغيير اللغة إلى العربية"))
        page.snack_bar.open = True
        page.update()
        # Sayfayı yeniden yükle
        import time
        time.sleep(0.5)
        page.clean()
        main(page)

    def change_to_english(e):
        if hasattr(page, 'dialog') and page.dialog:
            page.dialog.open = False
        page.current_language = "en"
        page.snack_bar = ft.SnackBar(content=ft.Text("Language changed to English"))
        page.snack_bar.open = True
        page.update()
        # Sayfayı yeniden yükle
        import time
        time.sleep(0.5)
        page.clean()
        main(page)

    def show_help(e):
        print("Help clicked!")  # للتحقق

        # استخدام صفحة جديدة للمساعدة
        def close_help():
            page.clean()
            main(page)

        def show_faq_item(question, answer):
            page.snack_bar = ft.SnackBar(content=ft.Text(answer))
            page.snack_bar.open = True
            page.update()

        # مسح الصفحة الحالية وإظهار صفحة المساعدة
        page.clean()

        page.add(
            ft.Container(
                content=ft.Column([
                    # شريط علوي مع زر الرجوع
                    ft.Row([
                        ft.IconButton(
                            icon=ft.Icons.ARROW_BACK,
                            on_click=lambda e: close_help()
                        ),
                        ft.Text(
                            "Yardım" if current_language == "tr"
                            else "المساعدة" if current_language == "ar"
                            else "Help",
                            size=20, weight=ft.FontWeight.BOLD
                        ),
                        ft.Container(expand=True),
                    ]),

                    ft.Divider(),

                    # أيقونة المساعدة
                    ft.Icon(ft.Icons.HELP_CENTER, size=60, color=ft.Colors.BLUE_600),

                    ft.Text(
                        "Size nasıl yardımcı olabiliriz?" if current_language == "tr"
                        else "كيف يمكننا مساعدتك؟" if current_language == "ar"
                        else "How can we help you?",
                        size=18,
                        weight=ft.FontWeight.BOLD,
                        text_align=ft.TextAlign.CENTER,
                        color=ft.Colors.BLUE_700
                    ),

                    ft.Container(height=20),

                    # دليل البدء السريع
                    ft.Text(
                        "🚀 Hızlı Başlangıç" if current_language == "tr"
                        else "🚀 البدء السريع" if current_language == "ar"
                        else "🚀 Quick Start",
                        size=16,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREEN_600
                    ),

                    ft.Container(
                        content=ft.Column([
                            ft.ListTile(
                                leading=ft.Text("1️⃣", size=20),
                                title=ft.Text(
                                    "E-posta adresinizi girin" if current_language == "tr"
                                    else "أدخل عنوان بريدك الإلكتروني" if current_language == "ar"
                                    else "Enter your email address"
                                ),
                                subtitle=ft.Text(
                                    "Geçerli bir e-posta adresi kullanın" if current_language == "tr"
                                    else "استخدم عنوان بريد إلكتروني صالح" if current_language == "ar"
                                    else "Use a valid email address"
                                )
                            ),
                            ft.ListTile(
                                leading=ft.Text("2️⃣", size=20),
                                title=ft.Text(
                                    "Güçlü bir şifre oluşturun" if current_language == "tr"
                                    else "أنشئ كلمة مرور قوية" if current_language == "ar"
                                    else "Create a strong password"
                                ),
                                subtitle=ft.Text(
                                    "En az 8 karakter, büyük/küçük harf ve rakam" if current_language == "tr"
                                    else "8 أحرف على الأقل، أحرف كبيرة/صغيرة وأرقام" if current_language == "ar"
                                    else "At least 8 characters, upper/lower case and numbers"
                                )
                            ),
                            ft.ListTile(
                                leading=ft.Text("3️⃣", size=20),
                                title=ft.Text(
                                    "Giriş butonuna tıklayın" if current_language == "tr"
                                    else "اضغط على زر تسجيل الدخول" if current_language == "ar"
                                    else "Click the login button"
                                ),
                                subtitle=ft.Text(
                                    "Bilgileriniz doğrulanacak" if current_language == "tr"
                                    else "سيتم التحقق من معلوماتك" if current_language == "ar"
                                    else "Your information will be verified"
                                )
                            )
                        ]),
                        padding=10,
                        bgcolor=ft.Colors.GREEN_50,
                        border_radius=10,
                        border=ft.border.all(1, ft.Colors.GREEN_200)
                    ),

                    ft.Container(height=20),

                    # الأسئلة الشائعة
                    ft.Text(
                        "❓ Sık Sorulan Sorular" if current_language == "tr"
                        else "❓ الأسئلة الشائعة" if current_language == "ar"
                        else "❓ Frequently Asked Questions",
                        size=16,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.PURPLE_600
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.LOCK, color=ft.Colors.ORANGE),
                        title=ft.Text(
                            "Şifremi unuttum, ne yapmalıyım?" if current_language == "tr"
                            else "نسيت كلمة المرور، ماذا أفعل؟" if current_language == "ar"
                            else "I forgot my password, what should I do?"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=lambda e: show_faq_item(
                            "Şifre sıfırlama",
                            "E-posta adresinizi girin, size sıfırlama bağlantısı göndereceğiz." if current_language == "tr"
                            else "أدخل عنوان بريدك الإلكتروني، سنرسل لك رابط إعادة التعيين." if current_language == "ar"
                            else "Enter your email address, we'll send you a reset link."
                        )
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.SECURITY, color=ft.Colors.RED),
                        title=ft.Text(
                            "Hesabım güvenli mi?" if current_language == "tr"
                            else "هل حسابي آمن؟" if current_language == "ar"
                            else "Is my account secure?"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=lambda e: show_faq_item(
                            "Güvenlik",
                            "Evet! Gelişmiş şifreleme ve iki faktörlü kimlik doğrulama kullanıyoruz." if current_language == "tr"
                            else "نعم! نستخدم تشفيراً متقدماً ومصادقة ثنائية." if current_language == "ar"
                            else "Yes! We use advanced encryption and two-factor authentication."
                        )
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.LANGUAGE, color=ft.Colors.BLUE),
                        title=ft.Text(
                            "Dili nasıl değiştirebilirim?" if current_language == "tr"
                            else "كيف يمكنني تغيير اللغة؟" if current_language == "ar"
                            else "How can I change the language?"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=lambda e: show_faq_item(
                            "Dil değiştirme",
                            "Üst menüden ⋮ → Dil seçeneğini kullanın." if current_language == "tr"
                            else "استخدم خيار ⋮ ← اللغة من القائمة العلوية." if current_language == "ar"
                            else "Use ⋮ → Language option from the top menu."
                        )
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.PRIVACY_TIP, color=ft.Colors.GREEN),
                        title=ft.Text(
                            "Verilerim nasıl korunuyor?" if current_language == "tr"
                            else "كيف يتم حماية بياناتي؟" if current_language == "ar"
                            else "How is my data protected?"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=lambda e: show_faq_item(
                            "Veri koruması",
                            "Verileriniz şifrelenir ve güvenli sunucularda saklanır." if current_language == "tr"
                            else "يتم تشفير بياناتك وحفظها في خوادم آمنة." if current_language == "ar"
                            else "Your data is encrypted and stored on secure servers."
                        )
                    ),

                    ft.Container(height=20),

                    # معلومات الاتصال للدعم
                    ft.Container(
                        content=ft.Column([
                            ft.Text(
                                "📞 Destek İletişim" if current_language == "tr"
                                else "📞 التواصل للدعم" if current_language == "ar"
                                else "📞 Support Contact",
                                size=16,
                                weight=ft.FontWeight.BOLD,
                                color=ft.Colors.TEAL_700
                            ),
                            ft.Text(
                                "Daha fazla yardıma mı ihtiyacınız var?\nBizimle iletişime geçin:" if current_language == "tr"
                                else "هل تحتاج إلى مزيد من المساعدة؟\nتواصل معنا:" if current_language == "ar"
                                else "Need more help?\nContact us:",
                                size=14,
                                color=ft.Colors.GREY_700,
                                text_align=ft.TextAlign.CENTER
                            ),
                            ft.Text(
                                "📧 <EMAIL>\n🌐 www.mursid.com\n📱 +90 555 123 4567",
                                size=14,
                                color=ft.Colors.TEAL_600,
                                text_align=ft.TextAlign.CENTER,
                                weight=ft.FontWeight.BOLD
                            )
                        ], spacing=10),
                        padding=15,
                        bgcolor=ft.Colors.TEAL_50,
                        border_radius=10,
                        border=ft.border.all(1, ft.Colors.TEAL_200)
                    ),

                    ft.Container(height=20),

                    # زر الرجوع
                    ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.ARROW_BACK),
                            ft.Text(
                                "Geri Dön" if current_language == "tr"
                                else "العودة" if current_language == "ar"
                                else "Go Back"
                            )
                        ], alignment=ft.MainAxisAlignment.CENTER),
                        width=200,
                        on_click=lambda e: close_help()
                    ),

                    # مساحة إضافية في الأسفل
                    ft.Container(height=30)

                ], spacing=10, scroll=ft.ScrollMode.ALWAYS),
                padding=20,
                expand=True
            )
        )

    def show_settings(e):
        print("Settings clicked!")  # للتحقق

        # استخدام صفحة جديدة بدلاً من BottomSheet
        def close_settings():
            page.clean()
            main(page)

        def toggle_notifications(e):
            status = "ON" if e.control.value else "OFF"
            page.snack_bar = ft.SnackBar(content=ft.Text(f"Notifications: {status}"))
            page.snack_bar.open = True
            page.update()

        def toggle_auto_login(e):
            status = "ON" if e.control.value else "OFF"
            page.snack_bar = ft.SnackBar(content=ft.Text(f"Auto Login: {status}"))
            page.snack_bar.open = True
            page.update()

        def show_security_settings():
            page.snack_bar = ft.SnackBar(content=ft.Text("Security settings opening..."))
            page.snack_bar.open = True
            page.update()

        # مسح الصفحة الحالية وإظهار صفحة الإعدادات
        page.clean()

        page.add(
            ft.Container(
                content=ft.Column([
                    # شريط علوي مع زر الرجوع
                    ft.Row([
                        ft.IconButton(
                            icon=ft.Icons.ARROW_BACK,
                            on_click=lambda e: close_settings()
                        ),
                        ft.Text(
                            "Ayarlar" if current_language == "tr"
                            else "الإعدادات" if current_language == "ar"
                            else "Settings",
                            size=20, weight=ft.FontWeight.BOLD
                        ),
                        ft.Container(expand=True),
                    ]),

                    ft.Divider(),

                    # الإعدادات
                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.NOTIFICATIONS, color=ft.Colors.BLUE),
                        title=ft.Text(
                            "Bildirimler" if current_language == "tr"
                            else "الإشعارات" if current_language == "ar"
                            else "Notifications"
                        ),
                        subtitle=ft.Text(
                            "Bildirimleri aç/kapat" if current_language == "tr"
                            else "تشغيل/إيقاف الإشعارات" if current_language == "ar"
                            else "Turn notifications on/off"
                        ),
                        trailing=ft.Switch(value=True, on_change=toggle_notifications)
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.LOGIN, color=ft.Colors.GREEN),
                        title=ft.Text(
                            "Otomatik Giriş" if current_language == "tr"
                            else "الدخول التلقائي" if current_language == "ar"
                            else "Auto Login"
                        ),
                        subtitle=ft.Text(
                            "Otomatik giriş yap" if current_language == "tr"
                            else "تسجيل دخول تلقائي" if current_language == "ar"
                            else "Automatic login"
                        ),
                        trailing=ft.Switch(value=False, on_change=toggle_auto_login)
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.SECURITY, color=ft.Colors.ORANGE),
                        title=ft.Text(
                            "Güvenlik" if current_language == "tr"
                            else "الأمان" if current_language == "ar"
                            else "Security"
                        ),
                        subtitle=ft.Text(
                            "Güvenlik ayarları" if current_language == "tr"
                            else "إعدادات الأمان" if current_language == "ar"
                            else "Security settings"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=lambda e: show_security_settings()
                    ),

                    ft.Container(height=30),

                    # زر الرجوع
                    ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.ARROW_BACK),
                            ft.Text(
                                "Geri Dön" if current_language == "tr"
                                else "العودة" if current_language == "ar"
                                else "Go Back"
                            )
                        ], alignment=ft.MainAxisAlignment.CENTER),
                        width=200,
                        on_click=lambda e: close_settings()
                    )

                ], spacing=10, scroll=ft.ScrollMode.AUTO),
                padding=20,
                expand=True
            )
        )

    def show_language_menu(e):
        print("Language menu clicked!")  # للتحقق

        # استخدام صفحة جديدة للغات
        def close_language():
            page.clean()
            main(page)

        def select_language(lang):
            print(f"Language selected: {lang}")  # للتحقق

            # تحديث اللغة في الصفحة
            page.current_language = lang
            print(f"Page language updated to: {page.current_language}")  # للتحقق

            # رسالة تأكيد تغيير اللغة
            page.snack_bar = ft.SnackBar(content=ft.Text(
                f"Dil değiştirildi: {texts[lang]['language_name']}" if lang == "tr"
                else f"تم تغيير اللغة: {texts[lang]['language_name']}" if lang == "ar"
                else f"Language changed: {texts[lang]['language_name']}"
            ))
            page.snack_bar.open = True
            page.update()

            # العودة الفورية للصفحة الرئيسية مع إعادة تحميل
            import time
            import threading
            def go_back():
                time.sleep(1.5)  # انتظار أقل
                print("Reloading page with new language...")  # للتحقق
                page.clean()
                main(page)  # إعادة تحميل الصفحة باللغة الجديدة
            threading.Thread(target=go_back).start()

        # مسح الصفحة الحالية وإظهار صفحة اللغات
        page.clean()

        page.add(
            ft.Container(
                content=ft.Column([
                    # شريط علوي مع زر الرجوع
                    ft.Row([
                        ft.IconButton(
                            icon=ft.Icons.ARROW_BACK,
                            on_click=lambda e: close_language()
                        ),
                        ft.Text(
                            "Dil Seçimi" if current_language == "tr"
                            else "اختيار اللغة" if current_language == "ar"
                            else "Language Selection",
                            size=20, weight=ft.FontWeight.BOLD
                        ),
                        ft.Container(expand=True),
                    ]),

                    ft.Divider(),

                    # أيقونة اللغة
                    ft.Icon(ft.Icons.LANGUAGE, size=60, color=ft.Colors.BLUE_600),

                    ft.Text(
                        "Tercih ettiğiniz dili seçin" if current_language == "tr"
                        else "اختر اللغة المفضلة لديك" if current_language == "ar"
                        else "Choose your preferred language",
                        text_align=ft.TextAlign.CENTER,
                        size=14,
                        color=ft.Colors.GREY_600
                    ),

                    ft.Container(height=20),

                    # اللغة التركية
                    ft.Container(
                        content=ft.ListTile(
                            leading=ft.Container(
                                content=ft.Text("🇹🇷", size=30),
                                width=50,
                                alignment=ft.alignment.center
                            ),
                            title=ft.Text(
                                "Türkçe",
                                size=18,
                                weight=ft.FontWeight.BOLD
                            ),
                            subtitle=ft.Text(
                                "Turkish • Türkiye",
                                size=12,
                                color=ft.Colors.GREY_600
                            ),
                            trailing=ft.Icon(
                                ft.Icons.CHECK_CIRCLE if current_language == "tr" else ft.Icons.RADIO_BUTTON_UNCHECKED,
                                color=ft.Colors.GREEN if current_language == "tr" else ft.Colors.GREY_400
                            ),
                            on_click=lambda e: select_language("tr")
                        ),
                        bgcolor=ft.Colors.GREEN_50 if current_language == "tr" else ft.Colors.TRANSPARENT,
                        border_radius=10,
                        border=ft.border.all(2, ft.Colors.GREEN_300) if current_language == "tr" else ft.border.all(1, ft.Colors.GREY_300),
                        padding=5
                    ),

                    ft.Container(height=10),

                    # اللغة العربية
                    ft.Container(
                        content=ft.ListTile(
                            leading=ft.Container(
                                content=ft.Text("🇸🇦", size=30),
                                width=50,
                                alignment=ft.alignment.center
                            ),
                            title=ft.Text(
                                "العربية",
                                size=18,
                                weight=ft.FontWeight.BOLD
                            ),
                            subtitle=ft.Text(
                                "Arabic • العربية",
                                size=12,
                                color=ft.Colors.GREY_600
                            ),
                            trailing=ft.Icon(
                                ft.Icons.CHECK_CIRCLE if current_language == "ar" else ft.Icons.RADIO_BUTTON_UNCHECKED,
                                color=ft.Colors.GREEN if current_language == "ar" else ft.Colors.GREY_400
                            ),
                            on_click=lambda e: select_language("ar")
                        ),
                        bgcolor=ft.Colors.GREEN_50 if current_language == "ar" else ft.Colors.TRANSPARENT,
                        border_radius=10,
                        border=ft.border.all(2, ft.Colors.GREEN_300) if current_language == "ar" else ft.border.all(1, ft.Colors.GREY_300),
                        padding=5
                    ),

                    ft.Container(height=10),

                    # اللغة الإنجليزية
                    ft.Container(
                        content=ft.ListTile(
                            leading=ft.Container(
                                content=ft.Text("🇺🇸", size=30),
                                width=50,
                                alignment=ft.alignment.center
                            ),
                            title=ft.Text(
                                "English",
                                size=18,
                                weight=ft.FontWeight.BOLD
                            ),
                            subtitle=ft.Text(
                                "English • United States",
                                size=12,
                                color=ft.Colors.GREY_600
                            ),
                            trailing=ft.Icon(
                                ft.Icons.CHECK_CIRCLE if current_language == "en" else ft.Icons.RADIO_BUTTON_UNCHECKED,
                                color=ft.Colors.GREEN if current_language == "en" else ft.Colors.GREY_400
                            ),
                            on_click=lambda e: select_language("en")
                        ),
                        bgcolor=ft.Colors.GREEN_50 if current_language == "en" else ft.Colors.TRANSPARENT,
                        border_radius=10,
                        border=ft.border.all(2, ft.Colors.GREEN_300) if current_language == "en" else ft.border.all(1, ft.Colors.GREY_300),
                        padding=5
                    ),

                    ft.Container(height=30),

                    # معلومات إضافية
                    ft.Container(
                        content=ft.Column([
                            ft.Text(
                                "ℹ️ Bilgi" if current_language == "tr"
                                else "ℹ️ معلومة" if current_language == "ar"
                                else "ℹ️ Information",
                                size=14,
                                weight=ft.FontWeight.BOLD,
                                color=ft.Colors.BLUE_700
                            ),
                            ft.Text(
                                "Dil değişikliği anında uygulanır ve uygulama yeniden başlatılır." if current_language == "tr"
                                else "يتم تطبيق تغيير اللغة فوراً وإعادة تشغيل التطبيق." if current_language == "ar"
                                else "Language change is applied instantly and the app restarts.",
                                size=12,
                                color=ft.Colors.GREY_600,
                                text_align=ft.TextAlign.CENTER
                            )
                        ], spacing=5),
                        padding=15,
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=10,
                        border=ft.border.all(1, ft.Colors.BLUE_200)
                    ),

                    ft.Container(height=20),

                    # زر الرجوع
                    ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.ARROW_BACK),
                            ft.Text(
                                "Geri Dön" if current_language == "tr"
                                else "العودة" if current_language == "ar"
                                else "Go Back"
                            )
                        ], alignment=ft.MainAxisAlignment.CENTER),
                        width=200,
                        on_click=lambda e: close_language()
                    ),

                    # مساحة إضافية في الأسفل
                    ft.Container(height=30)

                ], spacing=10, scroll=ft.ScrollMode.AUTO),
                padding=20,
                expand=True
            )
        )

    def show_theme(e):
        print("Theme clicked!")  # للتحقق

        # استخدام صفحة جديدة للمظاهر
        def close_theme():
            page.clean()
            main(page)

        def change_theme(theme_mode, message):
            page.theme_mode = theme_mode
            page.snack_bar = ft.SnackBar(content=ft.Text(message))
            page.snack_bar.open = True
            page.update()
            # العودة للصفحة الرئيسية بعد ثانيتين
            import time
            import threading
            def go_back():
                time.sleep(2)
                close_theme()
            threading.Thread(target=go_back).start()

        def light_theme(e):
            change_theme(ft.ThemeMode.LIGHT,
                "Açık tema seçildi" if current_language == "tr"
                else "تم اختيار المظهر الفاتح" if current_language == "ar"
                else "Light theme selected")

        def dark_theme(e):
            change_theme(ft.ThemeMode.DARK,
                "Koyu tema seçildi" if current_language == "tr"
                else "تم اختيار المظهر الداكن" if current_language == "ar"
                else "Dark theme selected")

        def system_theme(e):
            change_theme(ft.ThemeMode.SYSTEM,
                "Sistem teması seçildi" if current_language == "tr"
                else "تم اختيار مظهر النظام" if current_language == "ar"
                else "System theme selected")

        # مسح الصفحة الحالية وإظهار صفحة المظاهر
        page.clean()

        page.add(
            ft.Container(
                content=ft.Column([
                    # شريط علوي مع زر الرجوع
                    ft.Row([
                        ft.IconButton(
                            icon=ft.Icons.ARROW_BACK,
                            on_click=lambda e: close_theme()
                        ),
                        ft.Text(
                            "Tema Seçin" if current_language == "tr"
                            else "اختر المظهر" if current_language == "ar"
                            else "Choose Theme",
                            size=20, weight=ft.FontWeight.BOLD
                        ),
                        ft.Container(expand=True),
                    ]),

                    ft.Divider(),

                    # خيارات المظاهر
                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.LIGHT_MODE, color=ft.Colors.ORANGE, size=30),
                        title=ft.Text(
                            "Açık Tema" if current_language == "tr"
                            else "المظهر الفاتح" if current_language == "ar"
                            else "Light Theme"
                        ),
                        subtitle=ft.Text(
                            "Beyaz arka plan" if current_language == "tr"
                            else "خلفية بيضاء" if current_language == "ar"
                            else "White background"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=light_theme
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.DARK_MODE, color=ft.Colors.BLUE_GREY, size=30),
                        title=ft.Text(
                            "Koyu Tema" if current_language == "tr"
                            else "المظهر الداكن" if current_language == "ar"
                            else "Dark Theme"
                        ),
                        subtitle=ft.Text(
                            "Siyah arka plan" if current_language == "tr"
                            else "خلفية سوداء" if current_language == "ar"
                            else "Black background"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=dark_theme
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.SETTINGS_SYSTEM_DAYDREAM, color=ft.Colors.GREEN, size=30),
                        title=ft.Text(
                            "Sistem Teması" if current_language == "tr"
                            else "مظهر النظام" if current_language == "ar"
                            else "System Theme"
                        ),
                        subtitle=ft.Text(
                            "Sistem ayarlarını takip et" if current_language == "tr"
                            else "اتبع إعدادات النظام" if current_language == "ar"
                            else "Follow system settings"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=system_theme
                    ),

                    ft.Container(height=30),

                    # زر الرجوع
                    ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.ARROW_BACK),
                            ft.Text(
                                "Geri Dön" if current_language == "tr"
                                else "العودة" if current_language == "ar"
                                else "Go Back"
                            )
                        ], alignment=ft.MainAxisAlignment.CENTER),
                        width=200,
                        on_click=lambda e: close_theme()
                    )

                ], spacing=15, scroll=ft.ScrollMode.AUTO),
                padding=20,
                expand=True
            )
        )

    def show_privacy(e):
        print("Privacy clicked!")  # للتحقق

        # استخدام صفحة جديدة للخصوصية
        def close_privacy():
            page.clean()
            main(page)

        # مسح الصفحة الحالية وإظهار صفحة الخصوصية
        page.clean()

        page.add(
            ft.Container(
                content=ft.Column([
                    # شريط علوي مع زر الرجوع
                    ft.Row([
                        ft.IconButton(
                            icon=ft.Icons.ARROW_BACK,
                            on_click=lambda e: close_privacy()
                        ),
                        ft.Text(
                            "Gizlilik" if current_language == "tr"
                            else "الخصوصية" if current_language == "ar"
                            else "Privacy",
                            size=20, weight=ft.FontWeight.BOLD
                        ),
                        ft.Container(expand=True),
                    ]),

                    ft.Divider(),

                    # أيقونة الخصوصية
                    ft.Icon(ft.Icons.PRIVACY_TIP, size=60, color=ft.Colors.GREEN_600),

                    ft.Text(
                        "Gizliliğiniz bizim için önemli" if current_language == "tr"
                        else "خصوصيتك مهمة بالنسبة لنا" if current_language == "ar"
                        else "Your privacy is important to us",
                        size=18,
                        weight=ft.FontWeight.BOLD,
                        text_align=ft.TextAlign.CENTER,
                        color=ft.Colors.GREEN_700
                    ),

                    ft.Container(height=20),

                    # معلومات الخصوصية
                    ft.Container(
                        content=ft.Text(
                            texts[current_language]["privacy_text"],
                            size=14,
                            text_align=ft.TextAlign.CENTER,
                            color=ft.Colors.GREY_700
                        ),
                        padding=15,
                        bgcolor=ft.Colors.GREEN_50,
                        border_radius=10,
                        border=ft.border.all(1, ft.Colors.GREEN_200)
                    ),

                    ft.Container(height=30),

                    # زر الرجوع
                    ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.ARROW_BACK),
                            ft.Text(
                                "Geri Dön" if current_language == "tr"
                                else "العودة" if current_language == "ar"
                                else "Go Back"
                            )
                        ], alignment=ft.MainAxisAlignment.CENTER),
                        width=200,
                        on_click=lambda e: close_privacy()
                    ),

                    # مساحة إضافية في الأسفل
                    ft.Container(height=30)

                ], spacing=10, scroll=ft.ScrollMode.AUTO),
                padding=20,
                expand=True
            )
        )

    def show_admin_page():
        """عرض صفحة الأدمن"""
        print("Admin page opened!")  # للتحقق

        def close_admin():
            page.clean()
            main(page)

        def show_user_management(e):
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Kullanıcı yönetimi açılıyor..." if current_language == "tr"
                else "فتح إدارة المستخدمين..." if current_language == "ar"
                else "Opening user management..."
            ))
            page.snack_bar.open = True
            page.update()

        def show_system_settings(e):
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Sistem ayarları açılıyor..." if current_language == "tr"
                else "فتح إعدادات النظام..." if current_language == "ar"
                else "Opening system settings..."
            ))
            page.snack_bar.open = True
            page.update()

        def show_reports(e):
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Raporlar açılıyor..." if current_language == "tr"
                else "فتح التقارير..." if current_language == "ar"
                else "Opening reports..."
            ))
            page.snack_bar.open = True
            page.update()

        def show_system_logs(e):
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Sistem günlükleri açılıyor..." if current_language == "tr"
                else "فتح سجلات النظام..." if current_language == "ar"
                else "Opening system logs..."
            ))
            page.snack_bar.open = True
            page.update()

        def show_backup(e):
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Yedekleme açılıyor..." if current_language == "tr"
                else "فتح النسخ الاحتياطي..." if current_language == "ar"
                else "Opening backup..."
            ))
            page.snack_bar.open = True
            page.update()

        def show_admin_security(e):
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Güvenlik ayarları açılıyor..." if current_language == "tr"
                else "فتح إعدادات الأمان..." if current_language == "ar"
                else "Opening security settings..."
            ))
            page.snack_bar.open = True
            page.update()

        def admin_logout(e):
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Çıkış yapılıyor..." if current_language == "tr"
                else "جاري تسجيل الخروج..." if current_language == "ar"
                else "Logging out..."
            ))
            page.snack_bar.open = True
            page.update()
            import time
            import threading
            def go_back():
                time.sleep(1.5)
                close_admin()
            threading.Thread(target=go_back).start()

        # مسح الصفحة الحالية وإظهار صفحة الأدمن
        page.clean()

        page.add(
            ft.Container(
                content=ft.Column([
                    # شريط علوي مع زر الخروج
                    ft.Row([
                        ft.Text(
                            texts[current_language]["admin_dashboard"],
                            size=24,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.BLUE_700
                        ),
                        ft.Container(expand=True),
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.Icons.LOGOUT, size=16),
                                ft.Text(texts[current_language]["admin_logout"])
                            ], spacing=5),
                            on_click=admin_logout,
                            style=ft.ButtonStyle(bgcolor=ft.Colors.RED_600, color=ft.Colors.WHITE)
                        )
                    ]),

                    ft.Divider(),

                    # رسالة الترحيب
                    ft.Container(
                        content=ft.Row([
                            ft.Icon(ft.Icons.ADMIN_PANEL_SETTINGS, size=40, color=ft.Colors.BLUE_600),
                            ft.Column([
                                ft.Text(
                                    texts[current_language]["admin_welcome"],
                                    size=20,
                                    weight=ft.FontWeight.BOLD,
                                    color=ft.Colors.BLUE_700
                                ),
                                ft.Text(
                                    "<EMAIL>",
                                    size=14,
                                    color=ft.Colors.GREY_600
                                )
                            ], spacing=2, expand=True)
                        ]),
                        padding=15,
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=10,
                        border=ft.border.all(1, ft.Colors.BLUE_200)
                    ),

                    ft.Container(height=20),

                    # إحصائيات سريعة
                    ft.Text(
                        "📊 " + ("Hızlı İstatistikler" if current_language == "tr"
                        else "إحصائيات سريعة" if current_language == "ar"
                        else "Quick Statistics"),
                        size=16,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREEN_600
                    ),

                    ft.Row([
                        ft.Container(
                            content=ft.Column([
                                ft.Text("1,234", size=24, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_600),
                                ft.Text(texts[current_language]["admin_user_count"], size=12, color=ft.Colors.GREY_600)
                            ], alignment=ft.MainAxisAlignment.CENTER),
                            padding=15,
                            bgcolor=ft.Colors.BLUE_50,
                            border_radius=10,
                            border=ft.border.all(1, ft.Colors.BLUE_200),
                            expand=True
                        ),
                        ft.Container(width=10),
                        ft.Container(
                            content=ft.Column([
                                ft.Text("56", size=24, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_600),
                                ft.Text(texts[current_language]["admin_active_sessions"], size=12, color=ft.Colors.GREY_600)
                            ], alignment=ft.MainAxisAlignment.CENTER),
                            padding=15,
                            bgcolor=ft.Colors.GREEN_50,
                            border_radius=10,
                            border=ft.border.all(1, ft.Colors.GREEN_200),
                            expand=True
                        )
                    ]),

                    ft.Row([
                        ft.Container(
                            content=ft.Column([
                                ft.Text("✅", size=24),
                                ft.Text(texts[current_language]["admin_system_status"], size=12, color=ft.Colors.GREY_600)
                            ], alignment=ft.MainAxisAlignment.CENTER),
                            padding=15,
                            bgcolor=ft.Colors.GREEN_50,
                            border_radius=10,
                            border=ft.border.all(1, ft.Colors.GREEN_200),
                            expand=True
                        ),
                        ft.Container(width=10),
                        ft.Container(
                            content=ft.Column([
                                ft.Text("2h ago", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.ORANGE_600),
                                ft.Text(texts[current_language]["admin_last_backup"], size=12, color=ft.Colors.GREY_600)
                            ], alignment=ft.MainAxisAlignment.CENTER),
                            padding=15,
                            bgcolor=ft.Colors.ORANGE_50,
                            border_radius=10,
                            border=ft.border.all(1, ft.Colors.ORANGE_200),
                            expand=True
                        )
                    ]),

                    ft.Container(height=20),

                    # قائمة الإدارة
                    ft.Text(
                        "⚙️ " + ("Yönetim Menüsü" if current_language == "tr"
                        else "قائمة الإدارة" if current_language == "ar"
                        else "Management Menu"),
                        size=16,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.PURPLE_600
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.PEOPLE, color=ft.Colors.BLUE),
                        title=ft.Text(texts[current_language]["admin_users"]),
                        subtitle=ft.Text(
                            "Kullanıcıları yönet, ekle, sil" if current_language == "tr"
                            else "إدارة المستخدمين، إضافة، حذف" if current_language == "ar"
                            else "Manage users, add, delete"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=show_user_management
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.SETTINGS, color=ft.Colors.GREEN),
                        title=ft.Text(texts[current_language]["admin_settings"]),
                        subtitle=ft.Text(
                            "Sistem ayarlarını yapılandır" if current_language == "tr"
                            else "تكوين إعدادات النظام" if current_language == "ar"
                            else "Configure system settings"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=show_system_settings
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.ASSESSMENT, color=ft.Colors.ORANGE),
                        title=ft.Text(texts[current_language]["admin_reports"]),
                        subtitle=ft.Text(
                            "Raporları görüntüle ve dışa aktar" if current_language == "tr"
                            else "عرض وتصدير التقارير" if current_language == "ar"
                            else "View and export reports"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=show_reports
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.HISTORY, color=ft.Colors.PURPLE),
                        title=ft.Text(texts[current_language]["admin_logs"]),
                        subtitle=ft.Text(
                            "Sistem günlüklerini incele" if current_language == "tr"
                            else "فحص سجلات النظام" if current_language == "ar"
                            else "Review system logs"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=show_system_logs
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.BACKUP, color=ft.Colors.TEAL),
                        title=ft.Text(texts[current_language]["admin_backup"]),
                        subtitle=ft.Text(
                            "Yedekleme ve geri yükleme" if current_language == "tr"
                            else "النسخ الاحتياطي والاستعادة" if current_language == "ar"
                            else "Backup and restore"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=show_backup
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.SECURITY, color=ft.Colors.RED),
                        title=ft.Text(texts[current_language]["admin_security"]),
                        subtitle=ft.Text(
                            "Güvenlik ayarları ve izinler" if current_language == "tr"
                            else "إعدادات الأمان والصلاحيات" if current_language == "ar"
                            else "Security settings and permissions"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=show_admin_security
                    ),

                    ft.Container(height=30)

                ], spacing=10, scroll=ft.ScrollMode.ALWAYS),
                padding=20,
                expand=True
            )
        )

    def login_clicked(e):
        # Veri doğrulama
        if not email_field.value:
            email_field.error_text = texts[current_language]["email_error"]
            page.update()
            return

        if not password_field.value:
            password_field.error_text = texts[current_language]["password_error"]
            page.update()
            return

        # Hata mesajlarını temizle
        email_field.error_text = ""
        password_field.error_text = ""

        # التحقق من بيانات الأدمن
        if email_field.value == "<EMAIL>" and password_field.value == "admin123":
            # تسجيل دخول الأدمن - الانتقال إلى صفحة الأدمن
            success_message.value = f"{texts[current_language]['success_message']}{email_field.value}"
            success_message.visible = True
            login_button.disabled = True
            page.update()

            # انتظار قصير ثم الانتقال لصفحة الأدمن
            import time
            import threading
            def go_to_admin():
                time.sleep(1.5)
                show_admin_page()
            threading.Thread(target=go_to_admin).start()
        else:
            # تسجيل دخول عادي
            success_message.value = f"{texts[current_language]['success_message']}{email_field.value}"
            success_message.visible = True
            login_button.disabled = True
            page.update()

    def register_clicked(e):
        # Kayıt mantığı buraya eklenebilir
        page.snack_bar = ft.SnackBar(content=ft.Text(texts[current_language]["register_text"]))
        page.snack_bar.open = True
        page.update()

    def forgot_password_clicked(e):
        page.snack_bar = ft.SnackBar(content=ft.Text(texts[current_language]["forgot_text"]))
        page.snack_bar.open = True
        page.update()

    # Giriş alanları - responsive
    email_field = ft.TextField(
        label=texts[current_language]["email_label"],
        hint_text=texts[current_language]["email_hint"],
        expand=True,  # Mevcut alanı doldur
        prefix_icon=ft.Icons.EMAIL,
        keyboard_type=ft.KeyboardType.EMAIL,
        autofocus=True
    )

    password_field = ft.TextField(
        label=texts[current_language]["password_label"],
        hint_text=texts[current_language]["password_hint"],
        expand=True,  # Mevcut alanı doldur
        prefix_icon=ft.Icons.LOCK,
        password=True,
        can_reveal_password=True
    )

    # Uygulama düğmeleri - responsive
    login_button = ft.ElevatedButton(
        text=texts[current_language]["login_button"],
        width=250,  # عرض ثابت للزر
        height=45,
        on_click=login_clicked,
        style=ft.ButtonStyle(
            color=ft.Colors.WHITE,
            bgcolor=ft.Colors.BLUE_600,
        )
    )

    register_button = ft.ElevatedButton(
        text=texts[current_language]["register_button"],
        on_click=register_clicked,
        width=250,  # نفس عرض زر تسجيل الدخول
        height=40,  # ارتفاع مناسب
        style=ft.ButtonStyle(
            color=ft.Colors.WHITE,
            bgcolor=ft.Colors.GREEN_600,
        )
    )

    forgot_password_button = ft.TextButton(
        text=texts[current_language]["forgot_button"],
        on_click=forgot_password_clicked,
        style=ft.ButtonStyle(
            color=ft.Colors.BLUE_600,  # نص أزرق
        )
    )

    # رسالة النجاح
    success_message = ft.Text(
        value="",
        color=ft.Colors.GREEN_600,
        size=16,
        text_align=ft.TextAlign.CENTER,
        visible=False
    )



    # تخطيط الصفحة - responsive
    page.add(
        ft.Container(
            content=ft.Column([
                # قائمة الإعدادات في الأعلى
                ft.Row([
                    ft.Container(expand=True),  # مساحة فارغة لدفع القائمة لليمين
                    ft.PopupMenuButton(
                        icon=ft.Icons.MORE_VERT,  # أيقونة الثلاث نقاط
                        items=[
                            ft.PopupMenuItem(
                                text=texts[current_language]["about"],
                                icon=ft.Icons.INFO,
                                on_click=show_about
                            ),
                            ft.PopupMenuItem(
                                text=texts[current_language]["help"],
                                icon=ft.Icons.HELP,
                                on_click=show_help
                            ),
                            ft.PopupMenuItem(
                                text=texts[current_language]["settings"],
                                icon=ft.Icons.SETTINGS,
                                on_click=show_settings
                            ),
                            ft.PopupMenuItem(),  # Ayırıcı çizgi
                            ft.PopupMenuItem(
                                text=texts[current_language]["language"],
                                icon=ft.Icons.LANGUAGE,
                                on_click=show_language_menu
                            ),
                            ft.PopupMenuItem(
                                text=texts[current_language]["theme"],
                                icon=ft.Icons.PALETTE,
                                on_click=show_theme
                            ),
                            ft.PopupMenuItem(),  # Ayırıcı çizgi
                            ft.PopupMenuItem(
                                text=texts[current_language]["privacy"],
                                icon=ft.Icons.PRIVACY_TIP,
                                on_click=show_privacy
                            ),
                        ]
                    )
                ], alignment=ft.MainAxisAlignment.END),

                # العنوان والشعار - responsive
                ft.Text(
                    "🛡️",
                    size=60,  # حجم كبير للوجو
                    text_align=ft.TextAlign.CENTER
                ),
                ft.Text(
                    "Mürşid",
                    size=24,  # حجم كبير لاسم التطبيق
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.BLUE_600,
                    text_align=ft.TextAlign.CENTER
                ),
                ft.Text(
                    texts[current_language]["login_title"],
                    size=18,  # Alt başlık için küçük boyut
                    weight=ft.FontWeight.W_500,
                    color=ft.Colors.BLUE_700,
                    text_align=ft.TextAlign.CENTER
                ),
                ft.Text(
                    texts[current_language]["welcome"],
                    size=14,  # Orta boyut
                    color=ft.Colors.GREY_600,
                    text_align=ft.TextAlign.CENTER
                ),

                # مساحة فارغة
                ft.Container(height=10),  # مساحة صغيرة جداً

                # حقول الإدخال - responsive
                ft.ResponsiveRow([
                    ft.Column([
                        email_field,
                        ft.Container(height=8),
                        password_field,
                    ], col={"sm": 12, "md": 10, "lg": 8}, alignment=ft.MainAxisAlignment.CENTER)
                ], alignment=ft.MainAxisAlignment.CENTER),

                # مساحة فارغة
                ft.Container(height=10),  # مساحة صغيرة جداً

                # جميع الأزرار في المنتصف - responsive
                ft.Column([
                    login_button,
                    ft.Container(height=10),  # مساحة بين الأزرار
                    register_button,
                    ft.Container(height=8),   # مساحة بين الأزرار
                    forgot_password_button,
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),

                # رسالة النجاح
                success_message,

            ],
            alignment=ft.MainAxisAlignment.CENTER,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=8,  # مساحة متكيفة
            tight=True  # يتكيف مع المحتوى
            ),
            padding=ft.padding.symmetric(horizontal=20, vertical=20),  # حشو متكيف
            margin=ft.margin.only(left=10, right=10, top=10, bottom=10),  # هامش خارجي
            border_radius=10,
            bgcolor=ft.Colors.WHITE,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=15,
                color=ft.Colors.BLUE_GREY_300,
                offset=ft.Offset(0, 0),
            ),
            expand=True,  # يتوسع ليملأ المساحة المتاحة
            width=None,  # عرض متكيف
            height=None  # ارتفاع متكيف
        )
    )

if __name__ == "__main__":
    ft.app(target=main)
